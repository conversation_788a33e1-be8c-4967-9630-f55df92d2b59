import { QueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import queryKeys from './queryKeys';

export const showDetailDirect = async (queryClient: QueryClient, podcastId: string, isReplace = false) => {
  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: queryKeys.podcasts.item(podcastId),
    }),
    queryClient.prefetchQuery({
      queryKey: queryKeys.rates.byPodcastId(podcastId),
    }),
  ]);

  if (isReplace) {
    return router.replace({
      pathname: '/(app)/podcast/[podcastId]',
      params: {
        podcastId,
      },
    });
  }

  return router.push({
    pathname: '/(app)/podcast/[podcastId]',
    params: {
      podcastId,
    },
  });
};

export const episodeDetailDirect = async (queryClient: QueryClient, episodeId: string, isReplace = false) => {
  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: queryKeys.episodes.getEpisodeByIdRequest(episodeId),
    }),
    queryClient.prefetchQuery({
      queryKey: queryKeys.episodes.getEpisodeRateByIdRequest(episodeId),
    }),
  ]);

  if (isReplace) {
    return router.replace({
      pathname: '/(app)/episode/[episodeId]',
      params: {
        episodeId,
      },
    });
  }

  return router.push({
    pathname: '/(app)/episode/[episodeId]',
    params: {
      episodeId,
    },
  });
};
