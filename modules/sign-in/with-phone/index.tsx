import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { SocialsGridList } from '@/modules/signup/components/SocialsGridList';
import { router } from 'expo-router';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInEmail } from '../components/SignInEmail';
import { SignInPhoneForm } from '../components/SignInPhoneForm';

export default function SignInWithPhone() {
  const { styles } = useStyles(stylesheet);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.box} contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View>
          <Header />

          <Spacer height={48} />

          <ThemedText style={[styles.welcome, styles.textCenter]}>Welcome to Rabid</ThemedText>

          <Spacer height={16} />

          <ThemedText style={styles.textCenter} type='title'>
            Sign In to Your Account
          </ThemedText>

          <Spacer height={40} />

          <SignInPhoneForm />

          <Spacer height={40} />

          <View style={styles.orBox}>
            <View style={styles.orLine} />

            <ThemedText type='tinyMedium'>Or Using</ThemedText>

            <View style={styles.orLine} />
          </View>

          <Spacer height={24} />

          <SignInEmail />

          <Spacer height={20} />

          <SocialsGridList />
        </View>

        <View style={styles.footer}>
          <ThemedText type='small'>Don't have an account?</ThemedText>
          <TouchableOpacity onPress={() => router.replace('/sign-up')} activeOpacity={0.7}>
            <ThemedText style={styles.signUpText}>Sign Up</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  box: {
    flex: 1,
  },
  welcome: {
    opacity: 0.56,
  },
  textCenter: {
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    paddingTop: 4,
    paddingBottom: 32,
    paddingHorizontal: 24,
    gap: 24,
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    flexDirection: 'row',
    gap: 8,
  },
  signUpText: {
    ...theme.fw500,
    fontSize: 14,
    color: theme.colors.primary,
  },
  orBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  orLine: {
    flex: 1,
    height: 2,
    backgroundColor: theme.colors.neutralGrey,
  },
}));
