import { IPodcast, useGetPodcastsInfiniteQuery } from '@/apis/podcast';
import { FlatListAnimate, FlatListAnimateProps } from '@/components/FlatListAnimate';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { getItemSizeFlatList } from '@/utils/func';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams } from 'expo-router';
import { useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TopShowItem } from '../discover/components/TopShowItem';
import { TopShowItemSkeleton } from '../discover/components/TopShowItemSkeleton';

const itemSize = getItemSizeFlatList(24, 2, 22);

export const CategoryDetail = () => {
  const localParams = useLocalSearchParams();
  const categoryId = localParams?.id as string;
  const categoryName = localParams?.name as string;
  const { styles, theme } = useStyles(stylesheet);

  const { data, hasNextPage, isFetchingNextPage, fetchNextPage, isPending, refetch, isRefetching } =
    useGetPodcastsInfiniteQuery({
      limit: 10,
      page: 1,
      categoryId: Number(categoryId),
    });
  const podcasts = data?.pages?.map((page) => page.data).flat() ?? [];

  const handleRefresh = async () => {
    await refetch();
  };

  const renderItem = useCallback<FlatListAnimateProps<IPodcast>['renderItem']>(
    ({ item }) => (
      <TopShowItem
        name={item.title}
        authorName={item.authorName}
        star={Number(item.avgRate ?? 0)}
        rateCount={Number(item.rateCount ?? 0)}
        itemSize={itemSize}
        imageUrl={item.imageUrl}
        podcastId={Number(item.id)}
      />
    ),
    []
  );

  const keyExtractor = useCallback<FlatListAnimateProps<IPodcast>['keyExtractor']>((item) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => {
    if (!isPending) return null;

    return (
      <>
        <TopShowItemSkeleton itemSize={itemSize} />

        <TopShowItemSkeleton itemSize={itemSize} />
      </>
    );
  }, [isPending]);

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={theme.colors.discoverHeaderGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={[styles.header, styles.paddingContainer]}
      >
        <Header isBack title='' />
      </LinearGradient>

      <View style={[styles.paddingContainer, styles.contentBox]}>
        <ThemedText type='titleSemiBold' style={styles.title}>
          {categoryName}
        </ThemedText>

        <Spacer height={32} />

        <FlatListAnimate
          removeClippedSubviews
          // maxToRenderPerBatch={10}
          onEndReachedThreshold={0.5}
          data={podcasts}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          columnWrapperStyle={styles.columnWrapper}
          keyExtractor={keyExtractor}
          refreshing={isRefetching}
          onRefresh={handleRefresh}
          renderItem={renderItem}
          onEndReached={onEndReached}
          ListFooterComponent={renderSkeleton}
          ListFooterComponentStyle={styles.skeletonContainer}
        />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  paddingContainer: {
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: rt.insets.top,
  },
  contentBox: {
    paddingVertical: 24,
    flex: 1,
  },
  title: {},
  listContainer: {
    gap: 28,
  },
  columnWrapper: {
    gap: 22,
    justifyContent: 'space-between',
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 28,
  },
}));
