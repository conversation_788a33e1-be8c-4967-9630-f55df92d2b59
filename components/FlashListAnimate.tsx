import { Icons } from '@/assets/icons';
import { PanResponder, View } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  AnimatedProps,
  useComposedEventHandler,
} from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import * as Haptics from 'expo-haptics';
import { FlashList, FlashListProps } from '@shopify/flash-list';

const FlashListAnimated = Animated.createAnimatedComponent(FlashList);

export interface FlashListAnimateProps<T> extends AnimatedProps<FlashListProps<T>> {
  data: ReadonlyArray<T> | null | undefined;
  onRefresh?: () => Promise<void>;
  onScroll?: ReturnType<typeof useAnimatedScrollHandler>;
  // ref?: React.RefObject<Animated.FlatList<T>> | null;
}

const maxDistance = 150;

export function FlashListAnimate<T>({
  data,
  onRefresh,
  contentContainerStyle,
  refreshing,
  onScroll,
  ...rest
}: FlashListAnimateProps<T>) {
  const scrollPosition = useSharedValue(0);
  const pullDownPosition = useSharedValue(0);
  const iconOffset = useSharedValue('0deg');
  const isReadyToRefresh = useSharedValue(false);

  const { styles } = useStyles(stylesheet);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollPosition.value = event.contentOffset.y;
    },
  });

  const onPanRelease = async () => {
    pullDownPosition.value = withTiming(isReadyToRefresh.value ? maxDistance : 0, {
      duration: 180,
    });

    if (isReadyToRefresh.value) {
      isReadyToRefresh.value = false;

      iconOffset.value = withSequence(
        withTiming('180deg'),
        withRepeat(withTiming('360deg', { duration: 250 }), Infinity, true),
        withTiming('-180deg')
      );

      // A function that resets the animation
      const onRefreshComplete = () => {
        iconOffset.value = withTiming('0deg', { duration: 150 });

        setTimeout(() => {
          pullDownPosition.value = withTiming(0, { duration: 180 });
        }, 200);
      };

      await onRefresh?.();
      onRefreshComplete();
    }
  };

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: (e, gestureState) => {
      return !!onRefresh && scrollPosition.value <= 0 && gestureState.dy > 0;
    },
    onMoveShouldSetPanResponder: (e, gestureState) => {
      return !!onRefresh && scrollPosition.value <= 0 && gestureState.dy > 0;
    },
    onPanResponderMove: (event, gestureState) => {
      pullDownPosition.value = Math.max(Math.min(maxDistance, gestureState.dy), 0);

      if (pullDownPosition.value >= maxDistance / 2 && isReadyToRefresh.value === false) {
        isReadyToRefresh.value = true;
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      if (pullDownPosition.value < maxDistance / 2 && isReadyToRefresh.value === true) {
        isReadyToRefresh.value = false;
      }
    },
    onPanResponderRelease: onPanRelease,
    onPanResponderTerminate: onPanRelease,
  });

  const refreshContainerStyles = useAnimatedStyle(() => {
    return {
      height: pullDownPosition.value,
    };
  }, [pullDownPosition]);

  const refreshIconStyles = useAnimatedStyle(() => {
    return {
      opacity: Math.max(0, pullDownPosition.value - 25) / 50,
    };
  }, [pullDownPosition]);

  const iconStyles = useAnimatedStyle(() => {
    return {
      transform: [{ rotateY: iconOffset.value }, { scale: 0.7 }],
    };
  }, [iconOffset]);

  const combinedScrollHandler = useComposedEventHandler(onScroll ? [scrollHandler, onScroll] : [scrollHandler]);

  return (
    <Animated.View style={[styles.container]} {...panResponder.panHandlers}>
      <FlashListAnimated
        data={data}
        bounces={false}
        overScrollMode='auto'
        contentContainerStyle={contentContainerStyle}
        scrollEventThrottle={16}
        {...rest}
        onScroll={combinedScrollHandler}
      />

      <Animated.View style={[styles.refreshContainer, refreshContainerStyles]}>
        <View style={styles.blurView}>
          <Animated.View style={[styles.refreshIcon, refreshIconStyles]}>
            <Animated.View style={iconStyles}>
              <Icons.Rabid color='#0A0244' />
            </Animated.View>
          </Animated.View>
        </View>
      </Animated.View>
    </Animated.View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  blurView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  refreshContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 9999,
  },
  refreshIcon: {
    width: 50,
    height: 50,
    borderRadius: 999,
    resizeMode: 'contain',
    backgroundColor: theme.colors.primary,
    padding: 5,

    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
}));
