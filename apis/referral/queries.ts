import { useInfiniteQuery, UseInfiniteQueryOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { getReferralFriendsRequest } from './requests';
import { IGetReferralFriendsParams, IGetReferralFriendsResponse } from './types';
import queryKeys from '@/utils/queryKeys';

export const useGetInfiniteReferralFriendsQuery = (
  params: Omit<IGetReferralFriendsParams, 'page'> = {},
  options?: Omit<
    UseInfiniteQueryOptions<IGetReferralFriendsResponse, AxiosError>,
    'queryKey' | 'queryFn' | 'getNextPageParam' | 'initialPageParam'
  >
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.referral.friends(params),
    queryFn: ({ pageParam }) => getReferralFriendsRequest({ ...params, page: pageParam as number }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    maxPages: 3,
    ...options,
  });
};
