import { useEffect, useState } from 'react';
import { View } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import Animated, { useSharedValue, withTiming, withDelay, useAnimatedProps } from 'react-native-reanimated';

import StarRateFill from '@/assets/icons/start-rate-fill';
import { ThemedText } from '@/components/ThemedText';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

const AnimatedRect = Animated.createAnimatedComponent(Path);

interface BarGraphProps {
  data: { label: string; value: number }[];
  onChange?: (value: any) => void;
}

const screenWidth = UnistylesRuntime.screen.width;
const screenWidthWithPadding = screenWidth - 48 - 40;

const BarGraph = ({ data, onChange }: BarGraphProps) => {
  const { styles, theme } = useStyles(stylesheet);

  const maxValue = Math.max(...data.map((item) => item.value));
  const graphHeight = 72;
  const scale = maxValue !== 0 ? graphHeight / maxValue : 0;

  const barWidth = (screenWidthWithPadding - 18) / data.length;
  const labelOffset = barWidth / 2;

  const [selectedValue, setSelectedValue] = useState(null);

  const barHeights = data.map(() => useSharedValue(0));

  useEffect(() => {
    barHeights.forEach((height, index) => {
      height.value = withDelay(index * 100, withTiming(data[index].value * scale, { duration: 1000 }));
    });
  }, [scale, data, barHeights]);

  const handleBarPress = (value: any) => {
    if (selectedValue === value) {
      setSelectedValue(null);
      onChange?.(null);
      return;
    }
    setSelectedValue(value);
    onChange?.(value);
  };

  return (
    <View style={styles.card}>
      <View style={styles.container}>
        <Svg height={graphHeight} width={screenWidthWithPadding}>
          {data.map((item, index) => {
            const animatedProps = useAnimatedProps(() => {
              const x = index * (barWidth + 2);
              const width = barWidth;
              const baseY = graphHeight;
              const radius = 6;
              const h = Math.max(barHeights[index].value, 1);
              const topY = baseY - h > 0 ? baseY - h : 1;
              const rightX = x + width;
              const safeRadius = Math.min(radius, h / 2, width / 2);

              const path = `M${x},${baseY}
              L${x},${topY + safeRadius}
              A${safeRadius},${safeRadius} 0 0 1 ${x + safeRadius},${topY}
              L${rightX - safeRadius},${topY}
              A${safeRadius},${safeRadius} 0 0 1 ${rightX},${topY + safeRadius}
              L${rightX},${baseY} 
              Z`;

              return {
                d: path,
              };
            });

            return (
              <AnimatedRect
                key={item.label}
                fill={selectedValue === item.label ? theme.colors.stateWarning : theme.colors.neutralGrey}
                opacity={selectedValue === item.value ? 1 : 0.7}
                onPress={() => handleBarPress(item.label)}
                animatedProps={animatedProps}
              />
            );
          })}
        </Svg>

        {/* Label rendering stays the same */}
        <View style={[styles.labels, { width: screenWidthWithPadding }]}>
          {data.map((item, index) => (
            <ThemedText
              key={item.label}
              style={[
                styles.label,
                {
                  width: barWidth,
                  left: index * (barWidth + 2) + labelOffset - barWidth / 2,
                },
              ]}
            >
              {Number(item.label) % 1 === 0 && (
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <StarRateFill color={theme.colors.stateWarning} width={14} height={14} style={{ marginRight: 2 }} />
                  <ThemedText type='small'>{item.label}</ThemedText>
                </View>
              )}
            </ThemedText>
          ))}
        </View>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  card: {
    lineHeight: 18,
    color: theme.colors.neutralWhite,
    width: '100%',
  },
  chartText: {},
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.neutralCard,
    boxShadow: 'none',
    shadowRadius: 0,
    shadowOpacity: 0,
    shadowOffset: { width: 0, height: 0 },
  },
  labels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute',
    top: 82,
    left: 8,
    height: 18,
    width: '100%',
  },
  label: {
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 14,
    lineHeight: 18,
    position: 'absolute',
    color: theme.colors.neutralWhite,
  },
}));

export default BarGraph;
