import { UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  checkUserProfileUpdateValidRequest,
  planPaymentRequest,
  toggleFollowUserRequest,
  updateUserProfileMeRequest,
  updateUserProfileRequest,
  uploadImageToS3,
  uploadUserImage,
  addMethodSocialRequest,
  updateMethodSocialRequest,
  cancelPurchaseRequest,
} from './requests';
import {
  ICheckUserProfileUpdateValidPayload,
  ICheckUserProfileUpdateValidResponse,
  IPlanPaymentParams,
  IToggleFollowUserResponse,
  IUpdateUserProfile,
  IUploadImageToS3Params,
  IUploadUserImageResponse,
  IUserProfileById,
  IAddMethodSocialDto,
  IUpdateMethodSocialDto,
} from './types';
import queryKeys from '@/utils/queryKeys';
import { handleToggleFollowUser } from '../settled-handler/user';

export const useUpdateUserProfileMutation = (options?: UseMutationOptions<any, AxiosError, IUpdateUserProfile>) => {
  return useMutation({ mutationFn: (data) => updateUserProfileRequest(data), ...options });
};

export const useUpdateUserProfileMeMutation = (options?: UseMutationOptions<any, AxiosError, IUpdateUserProfile>) => {
  return useMutation({ mutationFn: (data) => updateUserProfileMeRequest(data), ...options });
};

export const useUploadUserImageMutation = (options?: UseMutationOptions<IUploadUserImageResponse, AxiosError, any>) => {
  return useMutation({ mutationFn: uploadUserImage, ...options });
};

export const useUploadImageToS3 = (options?: UseMutationOptions<any, AxiosError, IUploadImageToS3Params>) => {
  return useMutation({ mutationFn: uploadImageToS3, ...options });
};

export const planPaymentMutation = (options?: UseMutationOptions<any, AxiosError, IPlanPaymentParams>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: planPaymentRequest,
    onMutate(variables) {
      queryClient.setQueriesData({ queryKey: queryKeys.auth.profile() }, (oldData) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          status: variables.status,
        };
      });
    },
    onSettled(data, error, variables) {
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile() });
    },
    ...options,
  });
};

export const useCheckUserProfileUpdateValidMutation = (
  options?: UseMutationOptions<ICheckUserProfileUpdateValidResponse, AxiosError, ICheckUserProfileUpdateValidPayload>
) => {
  return useMutation({ mutationFn: checkUserProfileUpdateValidRequest, ...options });
};

export const useToggleFollowUserMutation = (
  options?: UseMutationOptions<IToggleFollowUserResponse, AxiosError, string | number>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: toggleFollowUserRequest,
    onMutate: (userId) => {
      queryClient.setQueriesData<IUserProfileById>({ queryKey: queryKeys.userProfile.byUserId(userId) }, (oldData) => {
        if (!oldData || oldData.id !== userId) return oldData;

        const isFollowed = oldData.isFollowed;

        return {
          ...oldData,
          isFollowed: !isFollowed,
          followersCount: isFollowed
            ? (Number(oldData.followersCount) - 1).toString()
            : (Number(oldData.followersCount) + 1).toString(),
        };
      });
    },
    onSettled(data, error, userId) {
      handleToggleFollowUser(queryClient, error, userId);
    },
    ...options,
  });
};

export const useAddMethodSocialMutation = (options?: UseMutationOptions<any, AxiosError, IAddMethodSocialDto>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addMethodSocialRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.userProfile.identities(),
      });
    },
    ...options,
  });
};

export const useUpdateMethodSocialMutation = (
  options?: UseMutationOptions<any, AxiosError, IUpdateMethodSocialDto>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateMethodSocialRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.userProfile.identities(),
      });
    },
    ...options,
  });
};

export const useCancelPurchaseMutation = (options?: UseMutationOptions<any, AxiosError, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => cancelPurchaseRequest(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.auth.profile(),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.userProfile.getSubscriptionStatus(),
      });
    },
    ...options,
  });
};
