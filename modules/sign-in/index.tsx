import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { router } from 'expo-router';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocials } from './components/SignInSocials';

export default function SignIn() {
  const { styles } = useStyles(stylesheet);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        bounces={false}
        style={styles.box}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Header />

        <View>
          <ThemedText style={[styles.welcome, styles.textCenter]}>Welcome to Rabid</ThemedText>

          <Spacer height={16} />

          <ThemedText style={styles.textCenter} type='title'>
            Sign In to Your Account
          </ThemedText>

          <Spacer height={64} />

          <SignInSocials />
        </View>

        <View style={styles.footer}>
          <ThemedText type='small'>Don't have an account?</ThemedText>
          <TouchableOpacity onPress={() => router.replace('/sign-up')} activeOpacity={0.7}>
            <ThemedText style={styles.signUpText}>Sign Up</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  box: {
    flex: 1,
  },
  welcome: {
    opacity: 0.56,
  },
  textCenter: {
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingVertical: 24,
    paddingHorizontal: 24,
    gap: 24,
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  signUpText: {
    ...theme.fw700,
    color: theme.colors.primary,
    fontSize: 14,
  },
}));
