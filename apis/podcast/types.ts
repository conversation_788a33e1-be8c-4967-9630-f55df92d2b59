import { ICategoryResponse } from '../category';
import { IPaginationMeta, IPaginationResponse } from '../params';
import { TUserSource } from '../user';

export interface IPodcast {
  id: string;
  title: string;
  authorName: string;
  description: string;
  imageUrl: string;
  authoremail: string;
  createdAt: string;
  avgRate: number | null;
  commentCount: string;
  categories: ICategoryResponse[];
  hasLiked: boolean;
  hasRated: boolean;
  rateCount: number;
  watched: {
    id: string | null;
    podcastId: string | null;
    userId: string | null;
    createdAt: string | null;
  };
}

export interface IGetPodcastByIdResponse extends IPodcast {}

export interface IGetPodcastListParams {
  limit: number;
  page: number;
  categoryId?: number;
  isTrending?: boolean;
}

export interface IGetPodcastRateListParams {
  limit: number;
  page: number;
  podcastId: string;
}

export interface IEpisode {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  isActive: boolean;
  ratingCount: number;
  ratingAverage: number;
  reviewCount: number;
  duration: string;
  releaseDate: string;
  podcastId: string;
  rateCount: string;
  avgRate: any;
  commentCount: string;
  hasLiked: boolean;
  hasRated: boolean;
  podcastTitle: string;
  watched: {
    id: string | null;
    podcastId: string | null;
    userId: string | null;
    createdAt: string | null;
  };
  podcastImageUrl: string;
  podcastDescription: string;
}

export interface IGetNewestEpisodesParams extends IGetPodcastListParams {}

export interface IGetNewestEpisodesResponse extends IPaginationResponse<IEpisode> {}

export interface IGetPodcastParams extends IGetPodcastListParams {
  search?: string;
  type?: 'podcast' | 'episode';
}

export interface IPodcastSearchInfinite {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  authorEmail: string | null;
  authorName: string | null;
  ratingCount: number;
  ratingAverage: number;
  numberOfEpisodes: number;
  reviewCount: number;
  avgRate: number | null;
  podcastId?: string;
  podcastTitle?: string;
  commentCount: string;
  type: 'podcast' | 'episode';
}

export interface IGetPodcastsSearchInfiniteResponse {
  meta: IPaginationMeta;
  data: IPodcastSearchInfinite[];
}

export interface IGetPodcastsResponse {
  meta: IPaginationMeta;
  data: IPodcast[];
}

export interface IGetPodcastRatesResponse {
  meta: IPaginationMeta;
  data: IPodcastRate[];
}

export interface IGetPodcastRateStatsResponse {
  averageRating: number;
  distribution: { count: string; rateValue: number }[];
  totalRatings: number;
  totalUsers: number;
}

export interface IGetPodcastByIdParams {
  podcastId: string;
}

// Podcast Rate Types
export interface IPodcastRate {
  id: string;
  podcastId: number;
  rate: number;
  userId: string;
  username: string;
  avatar: string;
  createdAt: string;
  tag?: string;
  source: TUserSource;
}

export interface IGetPodcastRateByPodcastIdParams {
  podcastId: string;
}

export interface IAddPodcastRateParams {
  podcastId?: number;
  rate: number;
}

export interface IUpdatePodcastRateParams {
  id: string;
  podcastId?: number;
  rate?: number;
}

export interface IDeletePodcastRateParams {
  id: string;
}

export interface IPodcastRateResponse {
  id: string;
  rate: number;
  podcastId: string;
  userId: string;
}

export interface IWatchlistAddRequest {
  podcastId: string;
  episodeId?: string;
}

export interface IMarkAsWatchRequest {
  podcastId: string;
  episodeId?: string;
}

export interface IMarkAsWatchResponse {
  userId: string;
  podcastId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  id: string;
}

export interface IWatchlistResponse {
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  id: string;
  userId: string;
  podcastId: string;
  episodeId: string;
}

export interface IGetMarkAsWatchRequest {
  podcastId: string;
}

export interface IGetWatchListRequest {
  podcastId: string;
}

export interface ILikePodcastRequest {
  podcastId: string;
}

export interface IAddFavoritePodcastBulkRequest {
  podcastIds: number[];
}

export interface ICommunityFeedItem {
  commentId: string;
  commentTitle: string;
  commentContent: string;
  createdAt: string;
  updatedAt: string;
  parentId: string;
  parentTitle: string;
  type: 'podcast' | 'episode';
  source: 'local' | 'podchaser';
  tag?: string;
  parentImageUrl: string;
  user: {
    id: string;
    username: string;
    avatar: string;
  };
  hasLiked: boolean;
  hasRated: boolean;
  userRate: number;
  podcastTitle: string;
  images: string[];
}

export interface IGetCommunityFeedResponse extends IPaginationResponse<ICommunityFeedItem> {}
