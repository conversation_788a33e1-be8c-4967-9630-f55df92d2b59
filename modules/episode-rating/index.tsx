import { IEpisodeRate, useGetEpisodeRateListQuery } from '@/apis/episode';
import { FlatListAnimate, FlatListAnimateProps } from '@/components/FlatListAnimate';
import { Header } from '@/components/ui/Header';
import queryKeys from '@/utils/queryKeys';
import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import { View } from 'react-native';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';
import { EpisodeRateReviewHeader } from './components/EpisodeRateReviewHeader';
import { EpisodeRateReviewItem } from './components/EpisodeRateReviewItem';
import { useCallback } from 'react';

const rt = UnistylesRuntime;
const EpisodeRatingScreen = () => {
  const { styles } = useStyles(stylesheet);

  const queryClient = useQueryClient();

  const localParams = useLocalSearchParams();
  const episodeId = localParams['episodeId'] as string;

  const {
    data: episodeRate,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useGetEpisodeRateListQuery({
    episodeId,
    limit: 10,
    page: 1,
  });

  const allRates = episodeRate?.pages.flatMap((page) => page.data);

  const handleRefetch = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: queryKeys.rates.episodeRateList() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodeByIdRequest() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.rates.episodeRateStats() }),
    ]);
  };

  const renderItem = useCallback<FlatListAnimateProps<IEpisodeRate>['renderItem']>(
    ({ item }) => <EpisodeRateReviewItem rateReviewData={item} />,
    []
  );

  const keyExtractor = useCallback<FlatListAnimateProps<IEpisodeRate>['keyExtractor']>(
    (item) => item.userId.toString(),
    []
  );

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <View style={[styles.container, { paddingTop: rt.insets.top, paddingBottom: rt.insets.bottom }]}>
      <Header isBack hideHeader />

      <FlatListAnimate
        removeClippedSubviews
        maxToRenderPerBatch={10}
        onEndReachedThreshold={0.5}
        data={allRates || []}
        showsVerticalScrollIndicator={false}
        keyExtractor={keyExtractor}
        ListHeaderComponent={<EpisodeRateReviewHeader episodeId={episodeId} />}
        onRefresh={handleRefetch}
        onEndReached={onEndReached}
        renderItem={renderItem}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
}));

export default EpisodeRatingScreen;
