import { useCommonStore } from '@/store/common';
import { router } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { OnboardingBottomAction } from './components/OnboardingBottomAction';
import { ONBOARDING_TOTAL_STEPS } from './utils';
import { OnboardingStep1 } from './components/OnboardingStep1';
import { OnboardingStep2 } from './components/OnboardingStep2';
import { OnboardingStep3 } from './components/OnboardingStep3';
import { OnboardingStep4 } from './components/OnboardingStep4';
import PagerView from 'react-native-pager-view';

export default function Onboarding() {
  const { styles } = useStyles(stylesheet);
  const [stepActive, setStepActive] = useState(0);

  const setIsFirstTime = useCommonStore.use.setIsFirstTime();
  const ref = useRef<PagerView>(null);

  const handleNext = () => {
    if (stepActive < ONBOARDING_TOTAL_STEPS - 1) {
      const nextStep = stepActive + 1;
      setStepActive(nextStep);
      ref.current?.setPage(nextStep);
    } else {
      router.replace('/sign-up');
    }
  };

  const renderStep = useCallback(({ item }: { item: number }) => {
    if (item === 1) return <OnboardingStep1 />;
    if (item === 2) return <OnboardingStep2 />;
    if (item === 3) return <OnboardingStep3 />;
    if (item === 4) return <OnboardingStep4 />;

    return null;
  }, []);

  const handleCheckIndex = useCallback((index: number) => {
    setStepActive(index);
  }, []);

  useEffect(() => {
    return () => {
      setIsFirstTime(false);
    };
  }, [setIsFirstTime]);

  return (
    <View style={styles.container}>
      <PagerView
        ref={ref}
        initialPage={stepActive}
        style={styles.container}
        onPageSelected={(event) => {
          handleCheckIndex(event.nativeEvent.position);
        }}
      >
        <OnboardingStep1 />

        <OnboardingStep2 />

        <OnboardingStep3 />

        <OnboardingStep4 />
      </PagerView>

      <OnboardingBottomAction stepActive={stepActive} onNext={handleNext} />
    </View>
  );
}

const stylesheet = createStyleSheet({
  container: {
    flex: 1,
  },
  animatedIntro: {
    justifyContent: 'center',
    ...StyleSheet.absoluteFillObject,
  },
});
