import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { useRouter } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { StarAndCommentCount } from './StarAndCommentCount';
import { showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';

type Props = {
  podcastId: number;
  name: string;
  authorName: string;
  star: number;
  rateCount: number;
  itemSize: number;
  imageUrl: string;
};

export const TopShowItem = ({ name, authorName, star, rateCount, itemSize, imageUrl, podcastId }: Props) => {
  const router = useRouter();
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handlePress = async () => {
    await showDetailDirect(queryClient, podcastId.toString());
  };

  return (
    <TouchableOpacity activeOpacity={0.7} style={[styles.itemBox, { width: itemSize }]} onPress={handlePress}>
      <ExpoImage source={{ uri: imageUrl }} style={[styles.itemImage, { width: itemSize, height: itemSize }]} />

      <Spacer height={14} />

      <ThemedText type='defaultMedium' style={styles.itemText} numberOfLines={1}>
        {name}
      </ThemedText>

      <Spacer height={4} />

      <ThemedText type='small' style={styles.authorName} numberOfLines={1}>
        {authorName}
      </ThemedText>

      <Spacer height={4} />

      <StarAndCommentCount star={star} rateCount={rateCount} />
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  itemBox: {},
  itemText: {
    color: theme.colors.neutralWhite,
  },
  itemImage: {
    objectFit: 'cover',
    borderRadius: 4,
  },
  authorName: {
    color: theme.colors.neutralWhite,
    opacity: 0.56,
  },
  starAndCommentsBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralWhite,
  },
}));
