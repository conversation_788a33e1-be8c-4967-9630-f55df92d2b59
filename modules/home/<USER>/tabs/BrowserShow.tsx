import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { IPodcast, useGetPodcastsInfiniteQuery } from '@/apis/podcast';
import { useCallback } from 'react';
import { View } from 'react-native';
import { ShowItem } from '../ShowItem';
import { getItemSizeFlatList } from '@/utils/func';
import { ShowsBanner } from '../ShowsBanner';
import { Skeleton } from 'moti/skeleton';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { FlatListAnimateProps } from '@/components/FlatListAnimate';
import { Spacer } from '@/components/Spacer';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {};

export const BrowserShow = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  const { data: podcastsData, isFetching } = useGetPodcastsInfiniteQuery({
    limit: 30,
    page: 1,
  });

  const podcasts = podcastsData?.pages?.map((page) => page.data).flat() ?? [];

  const renderItem = useCallback<FlatListAnimateProps<IPodcast>['renderItem']>(
    ({ item }) => <ShowItem item={item} itemSize={itemSize} />,
    []
  );

  const keyExtractor = useCallback((item: IPodcast) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => {
    return Array(3)
      .fill(0)
      .map((_, index) => (
        <View key={index} style={styles.columnWrapper}>
          <Skeleton width={itemSize} height={itemSize} radius={8} />
          <Skeleton width={itemSize} height={itemSize} radius={8} />
          <Skeleton width={itemSize} height={itemSize} radius={8} />
        </View>
      ));
  }, [styles.columnWrapper]);

  const renderSpacer = useCallback(() => <Spacer height={16} />, []);

  return (
    <TabFlashList
      bounces={false}
      showsVerticalScrollIndicator={false}
      data={podcasts}
      numColumns={3}
      estimatedItemSize={itemSize}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={<ShowsBanner />}
      ListFooterComponent={isFetching ? renderSkeleton : null}
      ListFooterComponentStyle={styles.skeletonContainer}
      contentContainerStyle={styles.contentContainer}
      ItemSeparatorComponent={renderSpacer}
      style={styles.container}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingBottom: 24,
    paddingHorizontal: 24,
  },
  columnWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 11,
  },
  skeletonContainer: {
    gap: 11,
  },
}));
