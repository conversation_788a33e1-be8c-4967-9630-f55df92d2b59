import { useRequestVerificationMutation } from '@/apis/auth';
import { useGetRegionInfoQuery } from '@/apis/user/queries';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import TextInput from '@/components/ui/TextInput';
import { DEFAULT_CODE_PENDING } from '@/constants/common';
import { REGEX } from '@/constants/regex';
import { SignUpSchema, SignUpSchemaData } from '@/lib/validations/auth';
import { VerifyCodeScreenParam } from '@/types/screen-param';
import { toastError } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SocialsGridList } from './components/SocialsGridList';
import { useCommonStore } from '@/store/common';
import { phoneTransform } from '@/utils/phoneUtils';
import * as Linking from 'expo-linking';
import { Header } from '@/components/ui/Header';

export default function Signup() {
  const { styles } = useStyles(stylesheet);
  const setIsFirstTime = useCommonStore.use.setIsFirstTime();
  const url = Linking.useURL();

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<SignUpSchemaData>({
    mode: 'onSubmit',
    resolver: zodResolver(SignUpSchema),
    defaultValues: {
      identifier: '',
    },
  });
  const { mutateAsync: signUp, isPending: isSubmitting } = useRequestVerificationMutation();

  const { data: regionInfo, isError: isGetRegionInfoError } = useGetRegionInfoQuery();

  const onSubmit = async (data: SignUpSchemaData) => {
    try {
      const signupRes = await signUp({ identifier: phoneTransform(data.identifier, regionInfo?.calling_code) });

      const identifier = phoneTransform(getValues('identifier'), regionInfo?.calling_code);
      const identifierType = REGEX.EMAIL.test(identifier) ? 'email' : 'phone';
      const remainingTime = signupRes?.remainingTime || DEFAULT_CODE_PENDING;

      const params = {
        identifier,
        identifierType,
        remainingTime,
      } as VerifyCodeScreenParam;

      if (url) {
        const { queryParams } = Linking.parse(url);
        params['referralCode'] = queryParams?.code as string;
      }

      router.push({
        pathname: '/(app)/verify-code',
        params: params as any,
      });
    } catch (error) {
      if (isGetRegionInfoError) {
        toastError({ message: 'Failed to detect your country code. Please include in your phone number.' });
        return;
      }
      toastError(error);
    }
  };

  useEffect(() => {
    setIsFirstTime(false);
  }, [setIsFirstTime]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.fullFlex}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <Header />
        <View>
          <ThemedText style={styles.welcomeText}>Welcome to Rabid</ThemedText>
          <ThemedText style={styles.headerText}>Let's Create Rabid Account</ThemedText>

          <ThemedText style={styles.inputLabel}>Email or Phone Number</ThemedText>
          <Controller
            control={control}
            name='identifier'
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                placeholder='Input Your Email or Phone Number'
                onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
                onBlur={onBlur}
                value={value}
                autoCapitalize='none'
                error={errors.identifier?.message}
              />
            )}
          />

          <Button
            type='default'
            style={[styles.signupButton]}
            onPress={handleSubmit(onSubmit)}
            isLoading={isSubmitting}
          >
            Sign Up
          </Button>

          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <ThemedText style={styles.dividerText}>Or Using</ThemedText>
            <View style={styles.divider} />
          </View>

          <SocialsGridList />
        </View>

        <View style={{ ...styles.footer }}>
          <ThemedText style={styles.footerText}>Already Have an Account?</ThemedText>
          <TouchableOpacity onPress={() => router.replace('/sign-in')} activeOpacity={0.7}>
            <ThemedText style={styles.signInText}>Sign In</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  fullFlex: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingVertical: 24,
    paddingHorizontal: 24,
    gap: 24,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
  logo: {
    alignSelf: 'center',
    height: 60,
    marginBottom: 48,
  },
  welcomeText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    color: theme.colors.whiteOpacity56,
  },
  headerText: {
    fontSize: 28,
    ...theme.fw600,
    marginBottom: 56,
    lineHeight: 32,
    textAlign: 'center',
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 12,
    ...theme.fw500,
  },
  signupButton: {
    marginVertical: 48,
    marginHorizontal: 31,
  },

  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  divider: {
    flex: 1,
    height: 2,
    borderRadius: 2,
    backgroundColor: theme.colors.neutralGrey,
  },
  dividerText: {
    marginHorizontal: 23,
    fontSize: 12,
    ...theme.fw500,
  },
  socialButton: {
    flex: 1,
    height: 48,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spotifyButton: {
    backgroundColor: '#1ED760',
    borderWidth: 1,
    borderColor: '#1ED760',
  },
  googleButton: {
    backgroundColor: theme.colors.neutralWhite,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  appleButton: {
    backgroundColor: theme.colors.neutralWhite,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  facebookButton: {
    backgroundColor: '#0866FF',
    borderWidth: 1,
    borderColor: '#0866FF',
  },
  socialIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  footerText: {
    color: '#FFF',
    fontSize: 14,
  },
  signInText: {
    color: '#D9FF03',
    ...theme.fw700,
    fontSize: 14,
  },
}));
