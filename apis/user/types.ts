import { IUserProfileResponse } from '../auth';
import { IPaginationResponse, ISource } from '../params';
import { IGetPodcastByIdResponse } from '../podcast';

export interface IUpdateUserProfile {
  username: string;
  avatar: string;
  email: string;
  dateOfBirth: string;
  bio?: string;
  referral?: string;
  userId: number;
  code?: string;
  phoneNumber?: string;
  isEmailChanged?: boolean;
  isPhoneChanged?: boolean;
}

export interface IUploadUserImageResponse {
  url: string;
  presigned: string;
}

export interface IUploadImageToS3Params {
  url: string;
  file: string;
}

export interface IPlanPaymentParams {
  status: 'choose_interest' | 'choose_podcast' | 'complete';
}

export type ICheckUserProfileUpdateValidPayload = Pick<IUpdateUserProfile, 'username' | 'email'> &
  Partial<Pick<IUpdateUserProfile, 'referral'>>;

export interface ICheckUserProfileUpdateValidResponse {
  remainingTime: number;
}

export interface IRegionInfoResponse {
  ip: string;
  success: boolean;
  type: string;
  continent: string;
  continent_code: string;
  country: string;
  country_code: string;
  region: string;
  region_code: string;
  city: string;
  latitude: number;
  longitude: number;
  is_eu: boolean;
  postal: string;
  calling_code: string;
  capital: string;
  borders: string;
  flag: Flag;
  connection: Connection;
  timezone: Timezone;
}

export interface Flag {
  img: string;
  emoji: string;
  emoji_unicode: string;
}

export interface Connection {
  asn: number;
  org: string;
  isp: string;
  domain: string;
}

export interface Timezone {
  id: string;
  abbr: string;
  is_dst: boolean;
  offset: number;
  utc: string;
  current_time: string;
}

export type TUserSource = 'podchaser' | 'local';

export type IUserProfileById = Pick<IUserProfileResponse, 'id' | 'username' | 'avatar' | 'type'> & {
  followersCount: string;
  followingCount: string;
  totalComments: string;
  bio?: string;
  isFollowed: boolean;
  isMe: boolean;
};

export interface IToggleFollowUserResponse {
  isFollowed: boolean;
}

export type IGetPostUserHistoryParams = {
  userId: string;
  limit: number;
  page?: number;
};

export type PostHistory = {
  content: string;
  updatedAt: string;
  isEdited: false;
  isActive: boolean;
  userId: string;
  parentId: string;
  parentTitle: string;
  parentImageUrl: string;
  podcastId: string;
  podcastTitle: string;
  type: 'podcast' | 'episode';
  images: string[];
  userRate: number;
} & Pick<IGetPodcastByIdResponse, 'id' | 'title' | 'createdAt' | 'hasLiked'>;

export interface IPostHistoryResponse extends IPaginationResponse<PostHistory> {}

export interface IGetReplyUserHistoryParams extends IGetPostUserHistoryParams {}
export interface IGetReplyUserHistoryResponse extends IPaginationResponse<ReplyHistory> {}

export type ReplyHistory = {
  id: string;
  content: string;
  userId: string;
  username: string;
  avatar: string;
  commentId: string;
  commentTitle: string;
  commentContent: string;
  commentUserId: string;
  commentUsername: string;
  commentUserAvatar: string;
  mediaId: string;
  mediaImageUrl: string;
  mediaTitle: string;
  parentMediaTitle: any;
  source: ISource;
  type: string;
  createdAt: string;
  updatedAt: string;
};

export interface IGetUserLikesPodcastsHistoryParams extends IGetPostUserHistoryParams {}
export interface IGetUserLikesPodcastsHistoryResponse extends IPaginationResponse<UserLikesPodcast> {}

export type UserLikesPodcast = {
  id: string;
  userId: string;
  podcastId: string;
  createdAt: string;
  title: string;
  imageUrl: string;
};

export type UserLikesEpisode = Pick<UserLikesPodcast, 'id' | 'userId' | 'createdAt'> & {
  episodeId: string;
  title: string;
  imageUrl: string;
};

export interface IGetUserLikesEpisodesHistoryParams extends IGetPostUserHistoryParams {}
export interface IGetUserLikesEpisodesHistoryResponse extends IPaginationResponse<UserLikesEpisode> {}

export type UserLikesPost = {
  userId: string;
  commentId: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  parentId: string;
  parentTitle: string;
  parentImageUrl: string;
  title: string;
  content: string;
  images: any[];
  createdAt: string;
  isEdited: boolean;
  type: string;
  source: ISource;
  likedAt: string;
};

export interface IGetUserLikesPostsHistoryParams extends IGetPostUserHistoryParams {}
export interface IGetUserLikesPostsHistoryResponse extends IPaginationResponse<UserLikesPost> {}

export interface Distribution {
  rateValue: number;
  count: number;
}

export interface IGetUserRateStatsReview {
  distribution: Distribution[];
  totalRatings: number;
  averageRating: string;
}

export interface IGetUserRateStatsReviewParams {
  userId: string;
  type?: 'podcast' | 'episode';
}

export interface IGetUserRatesReviewParams extends IGetPostUserHistoryParams {
  type?: 'podcast' | 'episode';
}
export interface IGetUserRatesReviewResponse extends IPaginationResponse<UserRateReview> {}

export type UserRateReview = {
  id: string;
  rate: number;
  mediaId: string;
  mediaName: string;
  mediaImageUrl: string;
  ratedAt: string;
  userId: string;
  type: string;
};

export type UserWatchlistHistory = Pick<UserRateReview, 'id' | 'mediaId' | 'mediaImageUrl' | 'userId' | 'type'> & {
  createdAt: string;
};

export interface IGetUserWatchlistHistoryParams extends IGetUserRatesReviewParams {}
export interface IGetUserWatchlistHistoryResponse extends IPaginationResponse<UserWatchlistHistory> {}

export interface IGetUserWatchedHistoryParams extends IGetUserWatchlistHistoryParams {}
export interface IGetUserWatchedHistoryResponse extends IGetUserWatchlistHistoryResponse {}

export enum IdentityStatus {
  MATCHED = 'matched',
  MISMATCHED = 'mismatch',
  UNTRACKABLE = 'untrackable',
}

export interface IUserIdentity {
  subject: string;
  providerId: number;
  providerType: string;
  providerName: string;
  status: IdentityStatus;
  icon: string;
}

export interface IAddMethodSocialDto {
  provider: string;
  token: string;
  type?: 'authentication' | 'access_token';
}

export interface IUpdateMethodSocialDto {
  provider: string;
  token: string;
  type?: 'authentication' | 'access_token';
}

export interface ISubscriptionMetadata {
  id: string;
  type: string;
  price: number;
  store: string;
  app_id: string;
  aliases: string[];
  currency: string;
  metadata: any;
  offer_code: any;
  product_id: string;
  app_user_id: string;
  environment: string;
  period_type: string;
  country_code: string;
  entitlement_id: any;
  renewal_number: number;
  tax_percentage: number;
  transaction_id: string;
  entitlement_ids: any;
  is_family_share: boolean;
  purchased_at_ms: number;
  expiration_at_ms: number;
  event_timestamp_ms: number;
  takehome_percentage: number;
  original_app_user_id: string;
  commission_percentage: number;
  presented_offering_id: string;
  subscriber_attributes: any;
  original_transaction_id: string;
  price_in_purchased_currency: number;
}

export interface ISubscription {
  createdAt: string;
  updatedAt: string;
  id: string;
  productId: string;
  offeringId: string;
  status: 'cancelled' | 'active' | 'expired';
  expiresAt: string;
  metadata: ISubscriptionMetadata;
}

export interface ISubscriptionStatus {
  subscription: ISubscription;
  userType: IUserProfileResponse['type'];
}
