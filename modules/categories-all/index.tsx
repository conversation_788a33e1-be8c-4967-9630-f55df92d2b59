import { ICategoryResponse } from '@/apis/category';
import { useGetCategoriesInfiniteQuery } from '@/apis/category/queries';
import { FlatListAnimateProps } from '@/components/FlatListAnimate';
import { SearchInput } from '@/components/SearchInput';
import { Spacer } from '@/components/Spacer';
import { Header } from '@/components/ui/Header';
import { getItemSizeFlatList } from '@/utils/func';
import { useDebouncedValue } from '@mantine/hooks';
import { useCallback, useEffect, useState } from 'react';
import { FlatList, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { CategoryItem } from './components/CategoryItem';
import { useQueryClient } from '@tanstack/react-query';
import { IconLoading } from '@/components/IconLoading';
import queryKeys from '@/utils/queryKeys';

const itemSize = getItemSizeFlatList(24, 2, 20);
const itemHeight = 109;

export const CategoriesAll = () => {
  const { styles } = useStyles(stylesheet);

  const [searchValue, setSearchValue] = useState('');
  const [isEndReachedCalled, setIsEndReachedCalled] = useState(true);

  const [searchDebounced] = useDebouncedValue(searchValue, 200);
  const queryClient = useQueryClient();

  const {
    data,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
  } = useGetCategoriesInfiniteQuery({
    limit: 20,
    page: 1,
    search: searchDebounced,
    isTrending: true,
  });
  const pages = data?.pages ?? [];
  const allCategories = pages?.flatMap((page) => page.data) ?? [];

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    return () => {
      queryClient.removeQueries({ queryKey: queryKeys.categories.categoriesInfinite() });
    };
  }, [queryClient, searchDebounced]);

  const renderItem = useCallback<FlatListAnimateProps<ICategoryResponse>['renderItem']>(
    ({ item }) => (
      <CategoryItem
        categoryId={item.id}
        title={item.name}
        totalPodcasts={item.totalPodcasts}
        itemSize={itemSize}
        podcastImage={item?.imageUrl}
      />
    ),
    []
  );

  const onEndReached = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback<FlatListAnimateProps<ICategoryResponse>['keyExtractor']>(
    (item) => item.id.toString(),
    []
  );

  const handleGetItemLayout = useCallback((item: any, index: number) => {
    const rowIndex = Math.floor(index / 2);
    const length = itemHeight;
    const offset = rowIndex * (itemHeight + 20);
    return { length, offset, index };
  }, []);

  const window = Math.ceil(allCategories.length > 50 ? allCategories.length / 4 : 21);

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header title='All Categories' isBack />
      </View>

      <View style={styles.containerPadding}>
        <Spacer height={32} />

        <SearchInput placeholder='Search Categories' value={searchValue} onChangeText={setSearchValue} />

        <Spacer height={32} />
      </View>

      <FlatList
        showsVerticalScrollIndicator={false}
        numColumns={2}
        // maxToRenderPerBatch={20}
        maintainVisibleContentPosition={{ minIndexForVisible: 0 }}
        data={allCategories}
        keyboardShouldPersistTaps='handled'
        keyExtractor={keyExtractor}
        getItemLayout={handleGetItemLayout}
        contentContainerStyle={styles.contentContainer}
        columnWrapperStyle={styles.columnWrapper}
        // onRefresh={handleRefetch}
        // onStartReachedThreshold={0.4}
        // onStartReached={handleStartReached}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        // onMomentumScrollBegin={handleMomentumScrollBegin}
        renderItem={renderItem}
        ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
        ListFooterComponentStyle={styles.skeletonContainer}
        // debug
        scrollEventThrottle={16}
        bounces={false}
        windowSize={window}
        maxToRenderPerBatch={window}
        // initialNumToRender={20}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    gap: 32,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  contentContainer: {
    gap: 20,
    paddingBottom: 20,
    paddingHorizontal: 24,
  },
  columnWrapper: {
    gap: 20,
    justifyContent: 'space-between',
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 20,
  },
}));
